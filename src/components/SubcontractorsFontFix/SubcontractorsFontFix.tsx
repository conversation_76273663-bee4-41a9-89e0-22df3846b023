"use client";

import { useEffect } from "react";

const SubcontractorsFontFix = () => {
  useEffect(() => {
    console.log("SubcontractorsFontFix: Component mounted");

    // Функція для примусового застосування шрифтів
    const forceApplyFonts = () => {
      console.log("SubcontractorsFontFix: Applying fonts");
      
      // Створюємо або оновлюємо style тег з шрифтами
      let styleTag = document.getElementById('subcontractors-font-fix');
      if (!styleTag) {
        styleTag = document.createElement('style');
        styleTag.id = 'subcontractors-font-fix';
        document.head.appendChild(styleTag);
      }

      styleTag.innerHTML = `
        /* Примусове застосування шрифтів для сторінки subcontractors */
        @font-face {
          font-family: 'RocaOne';
          src: url('/assets/fonts/RocaOne-BdIt.woff2') format('woff2');
          font-weight: 500 900;
          font-display: swap;
        }
        
        @font-face {
          font-family: 'RocaOne';
          src: url('/assets/fonts/RocaOne-Lt.woff2') format('woff2');
          font-weight: 100 400;
          font-display: swap;
        }
        
        @font-face {
          font-family: 'HelveticaNeueCyr';
          src: url('/assets/fonts/HelveticaNeueCyr-Roman.woff2') format('woff2');
          font-weight: 100 400;
          font-display: swap;
        }
        
        @font-face {
          font-family: 'HelveticaNeueCyr';
          src: url('/assets/fonts/HelveticaNeueCyr-Medium.woff2') format('woff2');
          font-weight: 500 900;
          font-display: swap;
        }

        /* Примусове застосування primary шрифту для всіх елементів, що його використовують */
        [class*="title"], 
        [class*="Title"],
        [class*="primaryFont"],
        [class*="mobileTitle"],
        [class*="statsHighlight"] {
          font-family: "RocaOne", "HelveticaNeueCyr", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
        }

        /* Примусове застосування secondary шрифту */
        html, body,
        [class*="subtitle"],
        [class*="Subtitle"],
        [class*="description"],
        [class*="Description"] {
          font-family: "HelveticaNeueCyr", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
        }
      `;

      // Також застосовуємо inline стилі до body
      document.body.style.fontFamily = '"HelveticaNeueCyr", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif';
      document.documentElement.style.fontFamily = '"HelveticaNeueCyr", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif';
    };

    // Функція для примусового перерендерингу
    const forceRerender = () => {
      console.log("SubcontractorsFontFix: Force rerender");
      
      // Знаходимо всі елементи з класами, що містять шрифти
      const elementsWithFonts = document.querySelectorAll('[class*="title"], [class*="Title"], [class*="primaryFont"], [class*="mobileTitle"], [class*="statsHighlight"]');
      
      elementsWithFonts.forEach((element) => {
        const htmlElement = element as HTMLElement;
        const originalDisplay = htmlElement.style.display;
        htmlElement.style.display = 'none';
        // Примусовий reflow
        htmlElement.offsetHeight;
        htmlElement.style.display = originalDisplay;
      });
    };

    // Обробник для pageshow (навігація назад)
    const handlePageShow = (event: PageTransitionEvent) => {
      if (event.persisted) {
        console.log("SubcontractorsFontFix: Page restored from bfcache");
        setTimeout(() => {
          forceApplyFonts();
          forceRerender();
        }, 100);
      }
    };

    // Обробник для focus
    const handleFocus = () => {
      console.log("SubcontractorsFontFix: Window focused");
      setTimeout(() => {
        forceApplyFonts();
        forceRerender();
      }, 100);
    };

    // Обробник для DOMContentLoaded
    const handleDOMContentLoaded = () => {
      console.log("SubcontractorsFontFix: DOM loaded");
      forceApplyFonts();
    };

    // Додаємо обробники
    window.addEventListener('pageshow', handlePageShow);
    window.addEventListener('focus', handleFocus);
    
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', handleDOMContentLoaded);
    } else {
      handleDOMContentLoaded();
    }

    // Початкове застосування
    forceApplyFonts();

    // Очищення при демонтажі
    return () => {
      console.log("SubcontractorsFontFix: Component unmounted");
      window.removeEventListener('pageshow', handlePageShow);
      window.removeEventListener('focus', handleFocus);
      document.removeEventListener('DOMContentLoaded', handleDOMContentLoaded);
      
      // Видаляємо style тег
      const styleTag = document.getElementById('subcontractors-font-fix');
      if (styleTag) {
        styleTag.remove();
      }
    };
  }, []);

  return null;
};

export default SubcontractorsFontFix;
