"use client";

import Head from "next/head";
import { useEffect } from "react";

const FontPreloader = () => {
  useEffect(() => {
    // Додаємо клас для індикації завантаження шрифтів
    document.documentElement.classList.add("font-loading");
    
    // Перевіряємо, чи шрифти завантажені
    const checkFontsLoaded = () => {
      if (document.fonts && document.fonts.ready) {
        document.fonts.ready.then(() => {
          document.documentElement.classList.remove("font-loading");
          document.documentElement.classList.add("font-loaded");
        });
      } else {
        // Fallback для старих браузерів
        setTimeout(() => {
          document.documentElement.classList.remove("font-loading");
          document.documentElement.classList.add("font-loaded");
        }, 3000);
      }
    };

    checkFontsLoaded();

    // Додаємо обробник для навігації
    const handlePageShow = (event: PageTransitionEvent) => {
      if (event.persisted) {
        // Сторінка відновлена з кешу браузера
        checkFontsLoaded();
      }
    };

    window.addEventListener("pageshow", handlePageShow);

    return () => {
      window.removeEventListener("pageshow", handlePageShow);
    };
  }, []);

  return (
    <Head>
      <link
        rel="preload"
        href="/assets/fonts/RocaOne-BdIt.woff2"
        as="font"
        type="font/woff2"
        crossOrigin="anonymous"
      />
      <link
        rel="preload"
        href="/assets/fonts/RocaOne-Lt.woff2"
        as="font"
        type="font/woff2"
        crossOrigin="anonymous"
      />
      <link
        rel="preload"
        href="/assets/fonts/HelveticaNeueCyr-Roman.woff2"
        as="font"
        type="font/woff2"
        crossOrigin="anonymous"
      />
      <link
        rel="preload"
        href="/assets/fonts/HelveticaNeueCyr-Medium.woff2"
        as="font"
        type="font/woff2"
        crossOrigin="anonymous"
      />
    </Head>
  );
};

export default FontPreloader;
