import { globalStyle } from "@vanilla-extract/css";
import { primaryFont, secondaryFont } from "./fonts.css";

// Забезпечуємо стабільність шрифтів при навігації
globalStyle("*", {
  fontDisplay: "swap",
});

// Додаємо preload для критичних шрифтів через CSS
globalStyle("head", {
  // Це буде додано через Next.js Head компонент
});

// Забезпечуємо, що шрифти завжди доступні
globalStyle("html", {
  fontFamily: `${secondaryFont}, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif`,
});

globalStyle("body", {
  fontFamily: `${secondaryFont}, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif`,
});

// Забезпечуємо стабільність для заголовків
globalStyle("h1, h2, h3, h4, h5, h6", {
  fontFamily: `${primaryFont}, ${secondaryFont}, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif`,
});

// Додаємо стилі для запобігання FOIT (Flash of Invisible Text)
globalStyle(".font-loading", {
  visibility: "hidden",
});

globalStyle(".font-loaded", {
  visibility: "visible",
});
