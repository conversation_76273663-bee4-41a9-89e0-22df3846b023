import { createVar, globalStyle } from "@vanilla-extract/css";
import { theme } from "./themes.css";
import { breakpoints } from "@/styles/constants.css";

globalStyle("*", {
  boxSizing: "border-box",
  WebkitTapHighlightColor: "rgba(255, 255, 255, 0)",
});

export const windowHeight = createVar();

globalStyle("p", {
  marginBlockStart: 0,
  marginBlockEnd: 0,
});

globalStyle("html, body", {
  scrollBehavior: "smooth",
  overscrollBehavior: "none",
  width: "100vw",
  maxWidth: "100vw",
  //overflowX: "hidden",
  margin: 0,
  padding: 0,
  backgroundColor: theme.colors.primary.ivory,
  color: theme.colors.primary.castletonGreen,
  fontFamily: theme.fonts.secondary,
  // Додаємо властивості для стабільності шрифтів
  fontDisplay: "swap",
  textRendering: "optimizeLegibility",
  WebkitFontSmoothing: "antialiased",
  MozOsxFontSmoothing: "grayscale",
});

globalStyle("html", {
  overflowX: "hidden",
  // This padding is used to prevent header from covering sections with an anchor behaviour (like in a refer page)
  scrollPaddingTop: 130,
  vars: {
    [windowHeight]: "100dvh",
  }
});

globalStyle("a", {
  color: "inherit",
});
globalStyle("ul", {
  marginBlockStart: 0,
  marginBlockEnd: 0,
});

globalStyle("h1, h2, h3, h4, h5, h6, p", {
  margin: 0,
});

globalStyle("input:-webkit-autofill, input:-webkit-autofill:hover, input:-webkit-autofill:focus, input:-webkit-autofill:active", {
  transition: "background-color 5000s ease-in-out 0s",
});

globalStyle(".main-earn", {
  marginBottom: 68,
});

globalStyle(".main-emergencies .plumbing-section", {
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 120,
    }
  }
});

globalStyle(".main-refer-page.main-central-heating-powerflush .shuffled-cards-list", {
  marginTop: 31,
  "@media": {
    [breakpoints.tablet]: {
      marginTop: 80,
    }
  }
});

// Add these styles to control scroll snap behavior with transitions
globalStyle(".scroll-snap-enabled", {
  scrollSnapType: "y mandatory",
  scrollBehavior: "smooth",
});

// Add a transition to smooth out height changes
globalStyle("[data-slice-type=\"preview_section\"]", {
  transition: "height 0.3s ease-out",
});

// Prevent height changes during scroll
globalStyle("html.is-scrolling [data-slice-type=\"preview_section\"]", {
  height: "var(--current-height)",
});
