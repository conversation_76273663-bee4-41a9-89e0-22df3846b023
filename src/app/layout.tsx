import AlertBar from "@/components/AlertBar";
import FontPreloader from "@/components/FontPreloader/FontPreloader";
import PageTopLoader from "@/components/PageTopLoader/PageTopLoader";
import StoreProvider from "@/components/StoreProvider";
import WindowResizeListener from "@/components/WindowResizeListener";
import "@/styles/index.css";
import { themeClass } from "@/styles/themes.css";
import {
  localBusinessJsonLd,
  organizationJsonLd,
  plumberJsonLd,
} from "@/utils/metadata";
import type { Metadata } from "next";
import Script from "next/script";
import { Suspense } from "react";
import { GoogleTagManager } from "@next/third-parties/google";

export const generateMetadata = ({ params }: { params: any }): Metadata => {
  const baseUrl =
    process.env.NEXT_PUBLIC_CURRENT_SITE_URL ||
    "https://www.pleasantplumbers.com/";

  const canonicalUrl = new URL(baseUrl);
  canonicalUrl.pathname = params?.slug?.join("/") || "";

  return {
    metadataBase: new URL(baseUrl),
    title: {
      template: "%s | Pleasant Plumbers",
      default: "Pleasant Plumbers",
    },
    description: "Pleasant Plumbers Site",
    alternates: {
      canonical: canonicalUrl.toString(),
    },
    robots: {
      index: true,
      follow: true,
    },
  };
};

export default async function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="en"
    >
      <head>
        <link
          rel="preload"
          href="/assets/fonts/RocaOne-BdIt.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/assets/fonts/RocaOne-Lt.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/assets/fonts/HelveticaNeueCyr-Roman.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/assets/fonts/HelveticaNeueCyr-Medium.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
      </head>
      <GoogleTagManager
        gtmId="GTM-MR9SGFF2"
      />
      <body
        className={themeClass}
      >
        <FontPreloader />
        <PageTopLoader />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationJsonLd),
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(plumberJsonLd) }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(localBusinessJsonLd),
          }}
        />
        <Suspense>
          <StoreProvider>{children}</StoreProvider>
          <AlertBar />
          <WindowResizeListener />
        </Suspense>
        <Script
          type="text/javascript"
          strategy="beforeInteractive"
          src="https://app.secureprivacy.ai/script/671a55f49c325afe037fa3f7.js"
        />
      </body>
    </html>
  );
}